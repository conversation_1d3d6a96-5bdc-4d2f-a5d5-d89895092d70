#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账单核对系统
实现四层匹配逻辑：
1. 金额精确匹配
2. 订单号精确匹配  
3. 合并支付检测
4. 净额匹配
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Set
from collections import defaultdict
import re
import logging
from datetime import datetime
import json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bill_reconciliation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class BillReconciliation:
    """账单核对系统主类"""
    
    def __init__(self):
        self.report_data = None  # 报表数据
        self.bank_data = None    # 银行数据
        self.matches = {
            'exact_amount': [],      # 金额精确匹配
            'order_number': [],      # 订单号匹配
            'combined_payment': [],  # 合并支付匹配
            'net_amount': []         # 净额匹配
        }
        self.unmatched = {
            'report_only': [],       # 报表单边
            'bank_only': []          # 银行单边
        }
        self.suspicious = []         # 可疑匹配
        
    def load_data(self, report_file: str, bank_files: List[str]) -> bool:
        """
        加载数据文件
        
        Args:
            report_file: 报表文件路径
            bank_files: 银行文件路径列表
            
        Returns:
            bool: 加载是否成功
        """
        try:
            # 加载报表数据
            logger.info(f"加载报表数据: {report_file}")
            self.report_data = pd.read_csv(report_file, encoding='utf-8')
            logger.info(f"报表数据加载成功，共 {len(self.report_data)} 条记录")
            
            # 加载银行数据
            bank_dataframes = []
            for bank_file in bank_files:
                logger.info(f"加载银行数据: {bank_file}")
                try:
                    # 尝试不同编码
                    for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                        try:
                            df = pd.read_csv(bank_file, encoding=encoding)
                            logger.info(f"使用编码 {encoding} 成功加载 {bank_file}")
                            bank_dataframes.append(df)
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        logger.error(f"无法解码文件 {bank_file}")
                        return False
                except Exception as e:
                    logger.error(f"加载银行文件 {bank_file} 失败: {e}")
                    return False
            
            # 合并银行数据
            if bank_dataframes:
                self.bank_data = pd.concat(bank_dataframes, ignore_index=True)
                logger.info(f"银行数据加载成功，共 {len(self.bank_data)} 条记录")
            else:
                logger.error("没有成功加载任何银行数据")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        logger.info("开始数据预处理...")
        
        # 处理报表数据
        if self.report_data is not None:
            # 确保金额列为数值类型
            self.report_data['fkje'] = pd.to_numeric(self.report_data['fkje'], errors='coerce')
            # 过滤掉金额为空或0的记录
            self.report_data = self.report_data[
                (self.report_data['fkje'].notna()) & 
                (self.report_data['fkje'] > 0)
            ].copy()
            # 添加索引列用于追踪
            self.report_data['report_index'] = range(len(self.report_data))
            logger.info(f"报表数据预处理完成，有效记录 {len(self.report_data)} 条")
        
        # 处理银行数据
        if self.bank_data is not None:
            # 根据银行数据的实际列名进行处理
            bank_columns = self.bank_data.columns.tolist()
            logger.info(f"银行数据列名: {bank_columns}")
            
            # 尝试识别金额列（通常包含数字和小数点）
            amount_col = None
            for col in bank_columns:
                if self.bank_data[col].dtype == 'object':
                    # 检查是否包含数字格式的金额
                    sample_values = self.bank_data[col].dropna().head(10)
                    if any(re.match(r'^\d+\.?\d*$', str(val).strip()) for val in sample_values):
                        amount_col = col
                        break
            
            if amount_col:
                # 清理金额数据
                self.bank_data['amount'] = pd.to_numeric(
                    self.bank_data[amount_col].astype(str).str.strip(), 
                    errors='coerce'
                )
                # 过滤1元记录（病历卡费用）
                self.bank_data = self.bank_data[
                    (self.bank_data['amount'].notna()) & 
                    (self.bank_data['amount'] != 1.0) &
                    (self.bank_data['amount'] > 0)
                ].copy()
                # 添加索引列用于追踪
                self.bank_data['bank_index'] = range(len(self.bank_data))
                logger.info(f"银行数据预处理完成，有效记录 {len(self.bank_data)} 条")
            else:
                logger.warning("未能识别银行数据中的金额列")
    
    def layer1_exact_amount_match(self):
        """第一层：金额精确匹配"""
        logger.info("执行第一层：金额精确匹配...")
        
        if self.report_data is None or self.bank_data is None:
            logger.error("数据未加载")
            return
        
        # 创建金额分组
        report_amounts = defaultdict(list)
        bank_amounts = defaultdict(list)
        
        # 按金额分组报表数据
        for idx, row in self.report_data.iterrows():
            amount = round(float(row['fkje']), 2)
            report_amounts[amount].append({
                'index': idx,
                'data': row.to_dict()
            })
        
        # 按金额分组银行数据
        for idx, row in self.bank_data.iterrows():
            amount = round(float(row['amount']), 2)
            bank_amounts[amount].append({
                'index': idx,
                'data': row.to_dict()
            })
        
        # 查找匹配
        matched_report_indices = set()
        matched_bank_indices = set()
        
        for amount in report_amounts.keys():
            if amount in bank_amounts:
                report_records = report_amounts[amount]
                bank_records = bank_amounts[amount]
                
                # 记录所有可能的匹配组合
                for report_record in report_records:
                    for bank_record in bank_records:
                        if (report_record['index'] not in matched_report_indices and 
                            bank_record['index'] not in matched_bank_indices):
                            
                            match = {
                                'amount': amount,
                                'report_record': report_record['data'],
                                'bank_record': bank_record['data'],
                                'match_type': 'exact_amount'
                            }
                            self.matches['exact_amount'].append(match)
                            matched_report_indices.add(report_record['index'])
                            matched_bank_indices.add(bank_record['index'])
                            break
        
        logger.info(f"第一层匹配完成，找到 {len(self.matches['exact_amount'])} 个金额匹配")

        return matched_report_indices, matched_bank_indices

    def layer2_order_number_match(self, matched_report_indices: Set, matched_bank_indices: Set):
        """第二层：订单号精确匹配"""
        logger.info("执行第二层：订单号精确匹配...")

        # 获取未匹配的数据
        unmatched_report = self.report_data[
            ~self.report_data.index.isin(matched_report_indices)
        ].copy()
        unmatched_bank = self.bank_data[
            ~self.bank_data.index.isin(matched_bank_indices)
        ].copy()

        # 创建订单号索引
        bank_order_dict = {}

        # 尝试从银行数据中提取订单号
        for idx, row in unmatched_bank.iterrows():
            # 查找可能的订单号列
            for col in row.index:
                val = str(row[col]).strip()
                # 匹配类似订单号的模式
                if re.match(r'^[A-Z0-9]{10,}', val):
                    bank_order_dict[val] = {
                        'index': idx,
                        'data': row.to_dict()
                    }

        # 匹配报表中的yhddh字段
        new_matched_report = set()
        new_matched_bank = set()

        for idx, row in unmatched_report.iterrows():
            yhddh = str(row['yhddh']).strip()
            if yhddh in bank_order_dict:
                bank_record = bank_order_dict[yhddh]

                match = {
                    'order_number': yhddh,
                    'report_record': row.to_dict(),
                    'bank_record': bank_record['data'],
                    'match_type': 'order_number'
                }
                self.matches['order_number'].append(match)
                new_matched_report.add(idx)
                new_matched_bank.add(bank_record['index'])

        logger.info(f"第二层匹配完成，找到 {len(self.matches['order_number'])} 个订单号匹配")

        # 更新已匹配索引
        matched_report_indices.update(new_matched_report)
        matched_bank_indices.update(new_matched_bank)

        return matched_report_indices, matched_bank_indices

    def layer3_combined_payment_match(self, matched_report_indices: Set, matched_bank_indices: Set):
        """第三层：合并支付检测"""
        logger.info("执行第三层：合并支付检测...")

        # 获取未匹配的数据
        unmatched_report = self.report_data[
            ~self.report_data.index.isin(matched_report_indices)
        ].copy()
        unmatched_bank = self.bank_data[
            ~self.bank_data.index.isin(matched_bank_indices)
        ].copy()

        # 按患者姓名分组报表数据
        patient_groups = defaultdict(list)
        for idx, row in unmatched_report.iterrows():
            patient_name = str(row['brxm']).strip()
            patient_groups[patient_name].append({
                'index': idx,
                'amount': float(row['fkje']),
                'data': row.to_dict()
            })

        # 查找同一患者的多笔费用组合
        new_matched_report = set()
        new_matched_bank = set()

        for patient_name, records in patient_groups.items():
            if len(records) > 1:
                # 计算各种组合的总金额
                from itertools import combinations

                for r in range(2, len(records) + 1):
                    for combo in combinations(records, r):
                        total_amount = round(sum(rec['amount'] for rec in combo), 2)

                        # 在银行数据中查找匹配的金额
                        for bank_idx, bank_row in unmatched_bank.iterrows():
                            if bank_idx in new_matched_bank:
                                continue

                            bank_amount = round(float(bank_row['amount']), 2)
                            if abs(bank_amount - total_amount) < 0.01:  # 允许小数点误差
                                match = {
                                    'patient_name': patient_name,
                                    'total_amount': total_amount,
                                    'report_records': [rec['data'] for rec in combo],
                                    'bank_record': bank_row.to_dict(),
                                    'match_type': 'combined_payment'
                                }
                                self.matches['combined_payment'].append(match)

                                # 标记为已匹配
                                for rec in combo:
                                    new_matched_report.add(rec['index'])
                                new_matched_bank.add(bank_idx)
                                break

        logger.info(f"第三层匹配完成，找到 {len(self.matches['combined_payment'])} 个合并支付匹配")

        # 更新已匹配索引
        matched_report_indices.update(new_matched_report)
        matched_bank_indices.update(new_matched_bank)

        return matched_report_indices, matched_bank_indices

    def layer4_net_amount_match(self, matched_report_indices: Set, matched_bank_indices: Set):
        """第四层：净额匹配（包含退款处理）"""
        logger.info("执行第四层：净额匹配...")

        # 获取未匹配的数据
        unmatched_report = self.report_data[
            ~self.report_data.index.isin(matched_report_indices)
        ].copy()
        unmatched_bank = self.bank_data[
            ~self.bank_data.index.isin(matched_bank_indices)
        ].copy()

        # 识别退款记录（负金额或退款标识）
        refund_reports = []
        payment_reports = []

        for idx, row in unmatched_report.iterrows():
            amount = float(row['fkje'])
            # 检查是否为退款（zfpb字段为1表示退款）
            if row.get('zfpb', 0) == 1 or amount < 0:
                refund_reports.append({
                    'index': idx,
                    'amount': abs(amount),
                    'data': row.to_dict()
                })
            else:
                payment_reports.append({
                    'index': idx,
                    'amount': amount,
                    'data': row.to_dict()
                })

        # 银行数据中的正负金额
        bank_positive = []
        bank_negative = []

        for idx, row in unmatched_bank.iterrows():
            amount = float(row['amount'])
            if amount > 0:
                bank_positive.append({
                    'index': idx,
                    'amount': amount,
                    'data': row.to_dict()
                })
            else:
                bank_negative.append({
                    'index': idx,
                    'amount': abs(amount),
                    'data': row.to_dict()
                })

        new_matched_report = set()
        new_matched_bank = set()

        # 查找退款+原支付的净额匹配
        for refund in refund_reports:
            for payment in payment_reports:
                if refund['index'] in new_matched_report or payment['index'] in new_matched_report:
                    continue

                # 检查是否为同一患者
                if refund['data']['brxm'] == payment['data']['brxm']:
                    net_amount = round(payment['amount'] - refund['amount'], 2)

                    # 在银行数据中查找匹配的净额
                    for bank_record in bank_positive:
                        if bank_record['index'] in new_matched_bank:
                            continue

                        if abs(bank_record['amount'] - net_amount) < 0.01:
                            match = {
                                'patient_name': refund['data']['brxm'],
                                'net_amount': net_amount,
                                'payment_record': payment['data'],
                                'refund_record': refund['data'],
                                'bank_record': bank_record['data'],
                                'match_type': 'net_amount'
                            }
                            self.matches['net_amount'].append(match)

                            new_matched_report.add(refund['index'])
                            new_matched_report.add(payment['index'])
                            new_matched_bank.add(bank_record['index'])
                            break

        logger.info(f"第四层匹配完成，找到 {len(self.matches['net_amount'])} 个净额匹配")

        # 更新已匹配索引
        matched_report_indices.update(new_matched_report)
        matched_bank_indices.update(new_matched_bank)

        return matched_report_indices, matched_bank_indices

    def detect_suspicious_matches(self, matched_report_indices: Set, matched_bank_indices: Set):
        """检测可疑匹配（金额相同但订单号不同）"""
        logger.info("检测可疑匹配...")

        # 获取未匹配的数据
        unmatched_report = self.report_data[
            ~self.report_data.index.isin(matched_report_indices)
        ].copy()
        unmatched_bank = self.bank_data[
            ~self.bank_data.index.isin(matched_bank_indices)
        ].copy()

        # 查找金额相同但可能存在其他差异的记录
        for _, report_row in unmatched_report.iterrows():
            report_amount = round(float(report_row['fkje']), 2)

            for _, bank_row in unmatched_bank.iterrows():
                bank_amount = round(float(bank_row['amount']), 2)

                if abs(report_amount - bank_amount) < 0.01:
                    # 金额匹配但未被其他层匹配，标记为可疑
                    suspicious = {
                        'amount': report_amount,
                        'report_record': report_row.to_dict(),
                        'bank_record': bank_row.to_dict(),
                        'reason': '金额匹配但订单号或其他信息不匹配'
                    }
                    self.suspicious.append(suspicious)

        logger.info(f"发现 {len(self.suspicious)} 个可疑匹配")

    def collect_unmatched_records(self, matched_report_indices: Set, matched_bank_indices: Set):
        """收集未匹配的记录"""
        logger.info("收集未匹配记录...")

        # 报表单边数据
        unmatched_report = self.report_data[
            ~self.report_data.index.isin(matched_report_indices)
        ].copy()

        for _, row in unmatched_report.iterrows():
            self.unmatched['report_only'].append(row.to_dict())

        # 银行单边数据
        unmatched_bank = self.bank_data[
            ~self.bank_data.index.isin(matched_bank_indices)
        ].copy()

        for _, row in unmatched_bank.iterrows():
            self.unmatched['bank_only'].append(row.to_dict())

        logger.info(f"报表单边记录: {len(self.unmatched['report_only'])} 条")
        logger.info(f"银行单边记录: {len(self.unmatched['bank_only'])} 条")

    def run_reconciliation(self):
        """执行完整的账单核对流程"""
        logger.info("开始执行账单核对...")

        # 数据预处理
        self.preprocess_data()

        # 初始化匹配索引
        matched_report_indices = set()
        matched_bank_indices = set()

        # 执行四层匹配
        matched_report_indices, matched_bank_indices = self.layer1_exact_amount_match()
        matched_report_indices, matched_bank_indices = self.layer2_order_number_match(
            matched_report_indices, matched_bank_indices
        )
        matched_report_indices, matched_bank_indices = self.layer3_combined_payment_match(
            matched_report_indices, matched_bank_indices
        )
        matched_report_indices, matched_bank_indices = self.layer4_net_amount_match(
            matched_report_indices, matched_bank_indices
        )

        # 检测可疑匹配
        self.detect_suspicious_matches(matched_report_indices, matched_bank_indices)

        # 收集未匹配记录
        self.collect_unmatched_records(matched_report_indices, matched_bank_indices)

        logger.info("账单核对完成")

        return self.generate_summary()

    def generate_summary(self) -> Dict:
        """生成匹配统计汇总"""
        summary = {
            'total_report_records': len(self.report_data) if self.report_data is not None else 0,
            'total_bank_records': len(self.bank_data) if self.bank_data is not None else 0,
            'matches': {
                'exact_amount': len(self.matches['exact_amount']),
                'order_number': len(self.matches['order_number']),
                'combined_payment': len(self.matches['combined_payment']),
                'net_amount': len(self.matches['net_amount'])
            },
            'total_matched': sum(len(matches) for matches in self.matches.values()),
            'unmatched': {
                'report_only': len(self.unmatched['report_only']),
                'bank_only': len(self.unmatched['bank_only'])
            },
            'suspicious_matches': len(self.suspicious)
        }

        # 计算匹配率
        if summary['total_report_records'] > 0:
            summary['match_rate'] = round(
                summary['total_matched'] / summary['total_report_records'] * 100, 2
            )
        else:
            summary['match_rate'] = 0

        return summary

    def export_detailed_report(self, output_file: str = 'reconciliation_report.xlsx'):
        """导出详细匹配报告到Excel"""
        logger.info(f"导出详细报告到: {output_file}")

        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 汇总统计
            summary_df = pd.DataFrame([self.generate_summary()])
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

            # 第一层匹配
            if self.matches['exact_amount']:
                exact_matches = []
                for match in self.matches['exact_amount']:
                    exact_matches.append({
                        '匹配类型': '金额精确匹配',
                        '金额': match['amount'],
                        '患者姓名': match['report_record'].get('brxm', ''),
                        '报表订单号': match['report_record'].get('yhddh', ''),
                        '银行记录': str(match['bank_record'])[:100] + '...'
                    })
                pd.DataFrame(exact_matches).to_excel(writer, sheet_name='金额精确匹配', index=False)

            # 第二层匹配
            if self.matches['order_number']:
                order_matches = []
                for match in self.matches['order_number']:
                    order_matches.append({
                        '匹配类型': '订单号精确匹配',
                        '订单号': match['order_number'],
                        '患者姓名': match['report_record'].get('brxm', ''),
                        '金额': match['report_record'].get('fkje', ''),
                        '银行记录': str(match['bank_record'])[:100] + '...'
                    })
                pd.DataFrame(order_matches).to_excel(writer, sheet_name='订单号精确匹配', index=False)

            # 第三层匹配
            if self.matches['combined_payment']:
                combined_matches = []
                for match in self.matches['combined_payment']:
                    combined_matches.append({
                        '匹配类型': '合并支付匹配',
                        '患者姓名': match['patient_name'],
                        '合并金额': match['total_amount'],
                        '记录数量': len(match['report_records']),
                        '银行记录': str(match['bank_record'])[:100] + '...'
                    })
                pd.DataFrame(combined_matches).to_excel(writer, sheet_name='合并支付匹配', index=False)

            # 第四层匹配
            if self.matches['net_amount']:
                net_matches = []
                for match in self.matches['net_amount']:
                    net_matches.append({
                        '匹配类型': '净额匹配',
                        '患者姓名': match['patient_name'],
                        '净额': match['net_amount'],
                        '支付金额': match['payment_record'].get('fkje', ''),
                        '退款金额': match['refund_record'].get('fkje', ''),
                        '银行记录': str(match['bank_record'])[:100] + '...'
                    })
                pd.DataFrame(net_matches).to_excel(writer, sheet_name='净额匹配', index=False)

            # 可疑匹配
            if self.suspicious:
                suspicious_df = []
                for sus in self.suspicious:
                    suspicious_df.append({
                        '金额': sus['amount'],
                        '患者姓名': sus['report_record'].get('brxm', ''),
                        '原因': sus['reason'],
                        '报表记录': str(sus['report_record'])[:100] + '...',
                        '银行记录': str(sus['bank_record'])[:100] + '...'
                    })
                pd.DataFrame(suspicious_df).to_excel(writer, sheet_name='可疑匹配', index=False)

            # 报表单边
            if self.unmatched['report_only']:
                report_unmatched = []
                for record in self.unmatched['report_only']:
                    report_unmatched.append({
                        '患者姓名': record.get('brxm', ''),
                        '金额': record.get('fkje', ''),
                        '订单号': record.get('yhddh', ''),
                        '完整记录': str(record)[:200] + '...'
                    })
                pd.DataFrame(report_unmatched).to_excel(writer, sheet_name='报表单边', index=False)

            # 银行单边
            if self.unmatched['bank_only']:
                bank_unmatched = []
                for record in self.unmatched['bank_only']:
                    bank_unmatched.append({
                        '金额': record.get('amount', ''),
                        '完整记录': str(record)[:200] + '...'
                    })
                pd.DataFrame(bank_unmatched).to_excel(writer, sheet_name='银行单边', index=False)

        logger.info("详细报告导出完成")


def main():
    """主函数"""
    print("=" * 60)
    print("账单核对系统")
    print("=" * 60)

    # 创建核对系统实例
    reconciler = BillReconciliation()

    # 数据文件路径
    report_file = "7.10报表数.csv"
    bank_files = ["7.10农商行(1).csv", "7.10江苏行(1).csv"]

    try:
        # 加载数据
        if not reconciler.load_data(report_file, bank_files):
            print("数据加载失败，程序退出")
            return

        # 执行核对
        summary = reconciler.run_reconciliation()

        # 打印汇总结果
        print("\n" + "=" * 60)
        print("核对结果汇总")
        print("=" * 60)
        print(f"报表总记录数: {summary['total_report_records']}")
        print(f"银行总记录数: {summary['total_bank_records']}")
        print(f"匹配率: {summary['match_rate']}%")
        print("\n匹配详情:")
        print(f"  第一层（金额精确匹配）: {summary['matches']['exact_amount']} 条")
        print(f"  第二层（订单号匹配）: {summary['matches']['order_number']} 条")
        print(f"  第三层（合并支付）: {summary['matches']['combined_payment']} 条")
        print(f"  第四层（净额匹配）: {summary['matches']['net_amount']} 条")
        print(f"  总匹配数: {summary['total_matched']} 条")
        print("\n未匹配记录:")
        print(f"  报表单边: {summary['unmatched']['report_only']} 条")
        print(f"  银行单边: {summary['unmatched']['bank_only']} 条")
        print(f"  可疑匹配: {summary['suspicious_matches']} 条")

        # 导出详细报告
        output_file = f"reconciliation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        reconciler.export_detailed_report(output_file)
        print(f"\n详细报告已导出到: {output_file}")

        # 保存匹配结果为JSON（用于进一步分析）
        json_file = f"reconciliation_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': summary,
                'matches': reconciler.matches,
                'unmatched': reconciler.unmatched,
                'suspicious': reconciler.suspicious
            }, f, ensure_ascii=False, indent=2, default=str)
        print(f"匹配数据已保存到: {json_file}")

    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        print(f"程序执行出错: {e}")


if __name__ == "__main__":
    main()
